import CrateServices
import Factory
import Swift<PERSON>

@MainActor
public final class ProfileRegisterViewModel: ObservableObject {
  @Published public var email: String = ""
  @Published public var password: String = ""
  @Published public var confirmPassword: String = ""
  @Published public var verificationCode: String = ""
  @Published public var registrationMessage: String = ""
  @Published public var registrationSuccessful: Bool = false
  @Published public var showVerificationCode: Bool = false
  @Published public var isLoading: Bool = false

  private let registrationService: RegistrationServiceProtocol
  private let userState: UserState

  public init() {
    registrationService = Container.shared.registrationService.resolve()
    userState = Container.shared.userState.resolve()
  }

  public func handleRegister() async {
    guard password == confirmPassword else {
      registrationMessage = "Passwords do not match"
      return
    }

    isLoading = true
    registrationMessage = ""

    do {
      let result = try await registrationService.startRegistration(
        email: email,
        password: password
      )

      switch result {
      case let .verificationRequired(sentTo):
        showVerificationCode = true
        registrationMessage = "Verification code sent to \(sentTo)"
      case .completed:
        // Registration completed without verification (unlikely with MSAL)
        registrationSuccessful = true
        registrationMessage = "Registration successful!"
      }

    } catch let error as RegistrationServiceError {
      switch error {
      case .emailAlreadyExists:
        registrationMessage = "Email already exists"
      case .invalidPassword:
        registrationMessage = "The password is invalid"
      case .registrationFailed:
        registrationMessage = "Registration failed"
      default:
        registrationMessage = "Registration failed: \(error)"
      }
    } catch {
      registrationMessage = "Registration failed: The operation couldn't be completed"
    }

    isLoading = false
  }

  public func submitVerificationCode() async {
    guard !verificationCode.isEmpty else {
      registrationMessage = "Verification code is required"
      return
    }

    isLoading = true
    registrationMessage = ""

    do {
      _ = try await registrationService.submitVerificationCode(verificationCode)
      showVerificationCode = false
      registrationSuccessful = true
      registrationMessage = "Registration successful!"

    } catch let error as RegistrationServiceError {
      switch error {
      case .invalidVerificationCode:
        registrationMessage = "Invalid verification code"
      case .registrationFailed:
        registrationMessage = "Verification failed"
      case .noActiveRegistration:
        registrationMessage = "No active registration found"
      default:
        registrationMessage = "Verification failed: \(error)"
      }
    } catch {
      registrationMessage = "Verification failed: The operation couldn't be completed"
    }

    isLoading = false
  }

  public func resendVerificationCode() async {
    isLoading = true

    do {
      try await registrationService.resendVerificationCode()
      registrationMessage = "Verification code resent"
    } catch {
      registrationMessage = "Failed to resend code"
    }

    isLoading = false
  }
}
