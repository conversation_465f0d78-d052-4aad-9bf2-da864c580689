import SwiftUI

struct ProfileRegisterView: View {
  @Environment(\.colorScheme) var colorScheme
  @StateObject private var viewModel: ProfileRegisterViewModel
  var onRegistrationComplete: () -> Void

  public init(onRegistrationComplete: @escaping () -> Void = {}) {
    _viewModel = StateObject(wrappedValue: ProfileRegisterViewModel())
    self.onRegistrationComplete = onRegistrationComplete
  }

  var body: some View {
    ZStack {
      VStack(spacing: 20) {
        Text("Register")
          .font(.largeTitle)
          .fontWeight(.bold)
          .foregroundColor(.primary)

        getField("Email", $viewModel.email)
        getField("Password", $viewModel.password, isSecure: true)
        getField("Confirm Password", $viewModel.confirmPassword, isSecure: true)

        getButton(
          action: {
            Task {
              await viewModel.handleRegister()
            }
          }, text: "Register"
        )
        .disabled(viewModel.isLoading)

        if !viewModel.registrationMessage.isEmpty {
          Text(viewModel.registrationMessage)
            .font(.subheadline)
            .foregroundColor(viewModel.registrationMessage.contains("successful") ? .green : .red)
            .padding(.top, 10)
        }
      }

      if viewModel.isLoading {
        Color.black.opacity(0.4)
          .ignoresSafeArea()

        ProgressView("Loading...")
          .progressViewStyle(CircularProgressViewStyle(tint: .white))
          .foregroundColor(.white)
          .scaleEffect(1.5)
      }
    }
    .sheet(isPresented: $viewModel.showVerificationCode) {
      VerificationCodeView(viewModel: viewModel)
    }
    .onChange(of: viewModel.registrationSuccessful) { _, success in
      if success {
        onRegistrationComplete()
      }
    }
  }

  @ViewBuilder
  private func getField(_ fieldName: String, _ text: Binding<String>, isSecure: Bool = false)
  -> some View {
    let baseField = Group {
      if isSecure {
        SecureField(fieldName, text: text)
      } else {
        TextField(fieldName, text: text)
          .autocapitalization(.none)
      }
    }

    baseField
      .padding()
      .cornerRadius(10)
      .overlay(
        RoundedRectangle(cornerRadius: 10)
          .stroke(Color.primary, lineWidth: 1)
      )
      .foregroundColor(.primary)
      .autocorrectionDisabled()
  }

  @ViewBuilder
  private func getButton(action: @escaping () -> Void, text: String) -> some View {
    CrateButtonComponent(
      title: text,
      action: action
    )
  }
}

#Preview {
  ProfileRegisterView()
}
