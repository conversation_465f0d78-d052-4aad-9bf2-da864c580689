import SwiftUI

struct ProfileAuthView: View {
  @StateObject private var viewModel = ProfileAuthViewModel()
  @Environment(\.presentationMode) private var presentationMode

  var body: some View {
    NavigationView {
      List {
        getAccountSection()
        getSignOutSection()
      }
      .navigationTitle("My Account")
      .navigationBarTitleDisplayMode(.inline)
      .navigationBarItems(trailing: getDoneButton())
    }
  }

  // MARK: - View Components

  @ViewBuilder
  private func getAccountSection() -> some View {
    Section(header: Text("Account")) {
      getEmailRow()
    }
  }

  @ViewBuilder
  private func getSignOutSection() -> some View {
    Section {
      getSignOutButton()
    }
  }

  // MARK: - Helper Views

  @ViewBuilder
  private func getEmailRow() -> some View {
    HStack {
      Text("Email")
      Spacer()
      Text(viewModel.email)
        .foregroundColor(.secondary)
    }
  }

  @ViewBuilder
  private func getSignOutButton() -> some View {
    Button(
      action: {
        viewModel.signOut {
          presentationMode.wrappedValue.dismiss()
        }
      },
      label: {
        HStack {
          Spacer()
          Text("Sign Out")
            .foregroundColor(.red)
          Spacer()
        }
      })
  }

  @ViewBuilder
  private func getDoneButton() -> some View {
    Button("Done") {
      presentationMode.wrappedValue.dismiss()
    }
  }
}

#Preview {
  ProfileAuthView()
}
