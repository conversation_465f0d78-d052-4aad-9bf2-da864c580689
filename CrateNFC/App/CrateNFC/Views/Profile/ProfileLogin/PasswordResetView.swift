import SwiftUI

struct PasswordResetView: View {
  @Environment(\.dismiss) private var dismiss
  @StateObject private var viewModel = PasswordResetViewModel()
  @State private var isPasswordVisible = false

  var body: some View {
    NavigationView {
      ZStack {
        VStack(spacing: 20) {
          Text("Reset Password")
            .font(.largeTitle)
            .fontWeight(.bold)
            .foregroundColor(.primary)

          if viewModel.showNewPasswordFields {
            newPasswordView
          } else if viewModel.showVerificationCode {
            verificationCodeView
          } else {
            resetRequestView
          }

          Spacer()
        }
        .padding()

        if viewModel.isLoading {
          Color.black.opacity(0.4)
            .ignoresSafeArea()

          ProgressView("Loading...")
            .progressViewStyle(CircularProgressViewStyle(tint: .white))
            .foregroundColor(.white)
            .scaleEffect(1.5)
        }
      }
      .navigationBarTitleDisplayMode(.inline)
      .toolbar {
        ToolbarItem(placement: .navigationBarTrailing) {
          But<PERSON>("Cancel") {
            dismiss()
          }
        }
      }
    }
  }

  private var resetRequestView: some View {
    VStack(spacing: 20) {
      Text("Enter your email address to receive a password reset code")
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)

      getField("Email", $viewModel.email)

      if !viewModel.resetMessage.isEmpty {
        Text(viewModel.resetMessage)
          .font(.subheadline)
          .foregroundColor(viewModel.resetMessage.contains("sent") ? .green : .red)
      }

      CrateButtonComponent(
        title: "Send Reset Code",
        action: {
          Task {
            await viewModel.requestPasswordReset()
          }
        }
      )
      .disabled(viewModel.isLoading)
    }
  }

  private var verificationCodeView: some View {
    VStack(spacing: 20) {
      Text("Enter the verification code sent to your email")
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)

      TextField("Verification Code", text: $viewModel.verificationCode)
        .keyboardType(.numberPad)
        .textContentType(.oneTimeCode)
        .padding()
        .cornerRadius(10)
        .overlay(
          RoundedRectangle(cornerRadius: 10)
            .stroke(Color.primary, lineWidth: 1)
        )
        .foregroundColor(.primary)

      if !viewModel.resetMessage.isEmpty {
        Text(viewModel.resetMessage)
          .font(.subheadline)
          .foregroundColor(viewModel.resetMessage.contains("successful") ? .green : .red)
      }

      VStack(spacing: 10) {
        CrateButtonComponent(
          title: "Submit",
          action: {
            Task {
              await viewModel.submitVerificationCode()
            }
          }
        )
        .disabled(viewModel.isLoading)

        Button("Resend Code") {
          Task {
            await viewModel.resendVerificationCode()
          }
        }
        .disabled(viewModel.isLoading)
      }
    }
  }

  private var newPasswordView: some View {
    VStack(spacing: 20) {
      Text("Enter your new password")
        .font(.body)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)

      getField(
        "New Password", $viewModel.newPassword, isSecure: true, isVisible: $isPasswordVisible)
      getField(
        "Confirm Password", $viewModel.confirmPassword, isSecure: true,
        isVisible: $isPasswordVisible)

      if !viewModel.resetMessage.isEmpty {
        Text(viewModel.resetMessage)
          .font(.subheadline)
          .foregroundColor(viewModel.resetMessage.contains("successful") ? .green : .red)
      }

      CrateButtonComponent(
        title: "Set New Password",
        action: {
          Task {
            await viewModel.submitNewPassword()
          }
        }
      )
      .disabled(viewModel.isLoading)
    }
  }

  @ViewBuilder
  private func getField(
    _ fieldName: String, _ text: Binding<String>, isSecure: Bool = false,
    isVisible: Binding<Bool>? = nil
  )
  -> some View {
    HStack {
      let baseField = Group {
        if isSecure && !(isVisible?.wrappedValue ?? false) {
          SecureField(fieldName, text: text)
        } else {
          TextField(fieldName, text: text)
            .autocapitalization(.none)
        }
      }

      baseField
        .foregroundColor(.primary)
        .autocorrectionDisabled()

      if isSecure, let visibilityBinding = isVisible {
        Button(action: {
          visibilityBinding.wrappedValue.toggle()
        }) {
          Image(systemName: visibilityBinding.wrappedValue ? "eye.slash" : "eye")
            .foregroundColor(.secondary)
        }
        .buttonStyle(PlainButtonStyle())
      }
    }
    .padding()
    .cornerRadius(10)
    .overlay(
      RoundedRectangle(cornerRadius: 10)
        .stroke(Color.primary, lineWidth: 1)
    )
  }
}
