import CrateServices
import Factory
import SwiftUI

public enum PasswordResetState {
  case initial
  case verificationRequired
  case newPasswordRequired
  case completed
}

@MainActor
public final class PasswordResetViewModel: ObservableObject {
  @Published public var email: String = ""
  @Published public var verificationCode: String = ""
  @Published public var newPassword: String = ""
  @Published public var confirmPassword: String = ""
  @Published public var isLoading: Bool = false
  @Published public var resetMessage: String = ""
  @Published public var showVerificationCode: Bool = false
  @Published public var showNewPasswordFields: Bool = false

  private let passwordResetService: PasswordResetServiceProtocol
  private var currentResetState: PasswordResetState = .initial

  public init(passwordResetService: PasswordResetServiceProtocol? = nil) {
    self.passwordResetService = passwordResetService ?? Container.shared.passwordResetService.resolve()
  }

  public func requestPasswordReset() async {
    guard !email.isEmpty else {
      resetMessage = "Please enter your email address"
      return
    }

    isLoading = true
    resetMessage = ""

    do {
      let result = try await passwordResetService.startPasswordReset(email: email)

      switch result {
      case .verificationRequired(let sentTo):
        currentResetState = .verificationRequired
        showVerificationCode = true
        showNewPasswordFields = false
        resetMessage = "Verification code sent to \(sentTo)"

      case .newPasswordRequired:
        currentResetState = .newPasswordRequired
        showVerificationCode = false
        showNewPasswordFields = true
        resetMessage = "Please enter your new password"

      case .completed:
        currentResetState = .completed
        showVerificationCode = false
        showNewPasswordFields = false
        resetMessage = "Password reset completed successfully!"
      }

    } catch PasswordResetServiceError.userNotFound {
      resetMessage = "No account found with that email address"
    } catch PasswordResetServiceError.resetFailed {
      resetMessage = "Failed to send reset code. Please try again."
    } catch PasswordResetServiceError.browserRequired {
      resetMessage = "Please use a web browser to reset your password"
    } catch {
      resetMessage = "An unexpected error occurred. Please try again."
    }

    isLoading = false
  }

  public func submitVerificationCode() async {
    guard !verificationCode.isEmpty else {
      resetMessage = "Please enter the verification code"
      return
    }

    isLoading = true
    resetMessage = ""

    do {
      let result = try await passwordResetService.submitVerificationCode(verificationCode)

      switch result {
      case .verificationRequired:
        resetMessage = "Please check your verification code and try again"

      case .newPasswordRequired:
        currentResetState = .newPasswordRequired
        showVerificationCode = false
        showNewPasswordFields = true
        resetMessage = "Please enter your new password"

      case .completed:
        currentResetState = .completed
        showVerificationCode = false
        showNewPasswordFields = false
        resetMessage = "Password reset completed successfully!"
      }

    } catch PasswordResetServiceError.invalidVerificationCode {
      resetMessage = "Invalid verification code. Please try again."
    } catch PasswordResetServiceError.noActiveReset {
      resetMessage = "No active reset session. Please start over."
      resetToInitialState()
    } catch PasswordResetServiceError.browserRequired {
      resetMessage = "Please use a web browser to complete the reset"
    } catch {
      resetMessage = "Failed to verify code. Please try again."
    }

    isLoading = false
  }

  public func submitNewPassword() async {
    guard !newPassword.isEmpty else {
      resetMessage = "Please enter a new password"
      return
    }

    guard newPassword == confirmPassword else {
      resetMessage = "Passwords do not match"
      return
    }

    isLoading = true
    resetMessage = ""

    do {
      let result = try await passwordResetService.submitNewPassword(newPassword)

      switch result {
      case .verificationRequired:
        // This shouldn't happen at this stage, but handle it
        resetMessage = "Unexpected state. Please start over."
        resetToInitialState()

      case .newPasswordRequired:
        resetMessage = "Password does not meet requirements. Please try again."

      case .completed:
        currentResetState = .completed
        showVerificationCode = false
        showNewPasswordFields = false
        resetMessage = "Password reset completed successfully! You can now sign in with your new password."
      }

    } catch PasswordResetServiceError.invalidPassword {
      resetMessage = "Password does not meet requirements. Please try again."
    } catch PasswordResetServiceError.noActiveReset {
      resetMessage = "No active reset session. Please start over."
      resetToInitialState()
    } catch {
      resetMessage = "Failed to set new password. Please try again."
    }

    isLoading = false
  }

  public func resendVerificationCode() async {
    isLoading = true
    resetMessage = ""

    do {
      try await passwordResetService.resendVerificationCode()
      resetMessage = "Verification code resent successfully"
    } catch {
      resetMessage = "Failed to resend code. Please try again."
    }

    isLoading = false
  }

  private func resetToInitialState() {
    currentResetState = .initial
    showVerificationCode = false
    showNewPasswordFields = false
    email = ""
    verificationCode = ""
    newPassword = ""
    confirmPassword = ""
  }
}
